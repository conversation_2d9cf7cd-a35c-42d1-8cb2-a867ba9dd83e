// analytics-tracker.js - Tracks adblock popup and conversion events with bot detection

// Bot Detection - Behavioral Analysis
let mouseMovements = 0;
let keystrokes = 0;
let scrollEvents = 0;
let clickEvents = 0;
let focusEvents = 0;
let sessionStartTime = Date.now();

// Initialize behavioral tracking
(function() {
    // Track mouse movements
    document.addEventListener('mousemove', function() {
        mouseMovements++;
    });

    // Track keyboard input
    document.addEventListener('keydown', function() {
        keystrokes++;
    });

    // Track scrolling
    document.addEventListener('scroll', function() {
        scrollEvents++;
    });

    // Track clicks
    document.addEventListener('click', function() {
        clickEvents++;
    });

    // Track focus events
    document.addEventListener('focus', function() {
        focusEvents++;
    }, true);
})();

// Enhanced analytics tracking with additional client-side data and bot detection
function gotham_send_analytics(eventType, status = 'pending') {
    // Check for localized AJAX variables first, then fallback to globals
    var ajaxUrl = '';
    var nonce = '';

    if (typeof gotham_ajax !== 'undefined') {
        ajaxUrl = gotham_ajax.ajaxurl;
        nonce = gotham_ajax.nonce;
    } else if (typeof ajaxurl !== 'undefined') {
        ajaxUrl = ajaxurl;
        if (typeof gotham_adblock_nonce !== 'undefined') {
            nonce = gotham_adblock_nonce;
        }
    } else {
        console.warn('Gotham Analytics: AJAX URL not available');
        return;
    }

    // Collect additional client-side data
    var additionalData = gotham_collect_client_data();

    var data = new FormData();
    data.append('action', 'gotham_adblock_track_event');
    data.append('event_type', eventType);
    data.append('status', status);
    data.append('nonce', nonce);

    // Add client-side data
    data.append('screen_resolution', additionalData.screen_resolution);
    data.append('timezone', additionalData.timezone);
    data.append('session_duration', additionalData.session_duration);
    data.append('page_url', additionalData.page_url);
    data.append('referrer_url', additionalData.referrer_url);

    // Add behavioral data for bot detection
    data.append('mouse_movements', additionalData.mouse_movements);
    data.append('keystrokes', additionalData.keystrokes);
    data.append('scroll_events', additionalData.scroll_events);
    data.append('click_events', additionalData.click_events);
    data.append('focus_events', additionalData.focus_events);
    data.append('time_on_page', additionalData.time_on_page);
    data.append('behavior_score', additionalData.behavior_score);
    data.append('is_likely_human', additionalData.is_likely_human);

    console.log('Gotham Analytics: Sending event:', eventType, 'to:', ajaxUrl);

    fetch(ajaxUrl, { method: 'POST', body: data })
        .then(res => {
            console.log('Gotham Analytics: Response status:', res.status);
            return res.json();
        })
        .then(json => {
            if (json && json.success) {
                console.log('Gotham Analytics: Event sent successfully:', eventType, json);
            } else {
                console.warn('Gotham Analytics: Event failed:', eventType, json);
                // Show debug info if available
                if (json && json.data) {
                    console.warn('Debug info:', json.data);
                    if (json.data.debug) {
                        console.warn('Additional debug:', json.data.debug);
                    }
                    if (json.data.message) {
                        console.warn('Error message:', json.data.message);
                    }
                }
            }
        })
        .catch(e => {
            console.error('Gotham Analytics: AJAX error for event:', eventType, e);
            console.error('AJAX URL was:', ajaxUrl);
        });
}

// Collect additional client-side data including behavioral analysis
function gotham_collect_client_data() {
    var sessionStart = gotham_get_session_start();
    var sessionDuration = sessionStart ? Math.floor((Date.now() - sessionStart) / 1000) : 0;
    var timeOnPage = Math.floor((Date.now() - sessionStartTime) / 1000);

    // Behavioral analysis
    var behaviorAnalysis = gotham_analyze_behavior(timeOnPage);

    return {
        screen_resolution: screen.width + 'x' + screen.height,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || '',
        session_duration: sessionDuration,
        page_url: window.location.href,
        referrer_url: document.referrer || '',
        // Behavioral data
        mouse_movements: mouseMovements,
        keystrokes: keystrokes,
        scroll_events: scrollEvents,
        click_events: clickEvents,
        focus_events: focusEvents,
        time_on_page: timeOnPage,
        behavior_score: behaviorAnalysis.score,
        is_likely_human: behaviorAnalysis.isLikelyHuman,
        browser_features: behaviorAnalysis.browserFeatures
    };
}

// Analyze user behavior to detect bots
function gotham_analyze_behavior(timeOnPage) {
    let score = 0;

    // Score based on interactions
    if (mouseMovements > 5) score += 2;
    if (keystrokes > 0) score += 3;
    if (scrollEvents > 2) score += 2;
    if (clickEvents > 0) score += 3;
    if (focusEvents > 0) score += 1;

    // Time-based scoring
    if (timeOnPage > 2 && timeOnPage < 3600) score += 2; // Reasonable time
    if (timeOnPage > 10) score += 1; // Spent some time

    // Browser features check
    const browserFeatures = gotham_check_browser_features();
    score += browserFeatures.score;

    // Determine if likely human (score > 8 indicates human behavior)
    const isLikelyHuman = score > 8 &&
                         (mouseMovements > 3 || keystrokes > 0) &&
                         timeOnPage > 1 &&
                         browserFeatures.score > 5;

    return {
        score: score,
        isLikelyHuman: isLikelyHuman,
        browserFeatures: browserFeatures
    };
}

// Check browser features to detect headless browsers
function gotham_check_browser_features() {
    let score = 0;
    const features = {};

    // Basic browser objects
    if (typeof window.navigator !== 'undefined') { features.navigator = true; score += 1; }
    if (typeof window.screen !== 'undefined') { features.screen = true; score += 1; }
    if (typeof document.cookie !== 'undefined') { features.cookies = true; score += 1; }
    if (typeof window.localStorage !== 'undefined') { features.localStorage = true; score += 1; }
    if (typeof window.sessionStorage !== 'undefined') { features.sessionStorage = true; score += 1; }
    if (typeof window.history !== 'undefined') { features.history = true; score += 1; }

    // WebGL support (most bots don't have this)
    try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) { features.webgl = true; score += 3; }
    } catch (e) { features.webgl = false; }

    // Check for headless browser indicators
    if (window.navigator.webdriver) { features.webdriver = true; score -= 5; }
    if (window.phantom) { features.phantom = true; score -= 5; }
    if (window.callPhantom) { features.callPhantom = true; score -= 5; }

    // Check user agent
    const ua = navigator.userAgent.toLowerCase();
    if (ua.includes('headless')) { features.headlessUA = true; score -= 3; }
    if (ua.includes('phantom')) { features.phantomUA = true; score -= 3; }
    if (ua.includes('selenium')) { features.seleniumUA = true; score -= 3; }

    // Check for missing plugins (real browsers usually have some)
    if (navigator.plugins && navigator.plugins.length === 0) {
        features.noPlugins = true;
        score -= 2;
    }

    // Check for realistic screen dimensions
    if (screen.width && screen.height) {
        if (screen.width < 100 || screen.height < 100) {
            features.unrealisticScreen = true;
            score -= 2;
        }
    }

    features.score = score;
    return features;
}

// Session management
function gotham_get_session_start() {
    var sessionStart = sessionStorage.getItem('gotham_session_start');
    if (!sessionStart) {
        sessionStart = Date.now();
        sessionStorage.setItem('gotham_session_start', sessionStart);
    }
    return parseInt(sessionStart);
}

// Track page visibility changes for better session tracking
function gotham_track_page_visibility() {
    var hidden = 'hidden';
    var visibilityChange = 'visibilitychange';

    if (typeof document.hidden !== 'undefined') {
        hidden = 'hidden';
        visibilityChange = 'visibilitychange';
    } else if (typeof document.msHidden !== 'undefined') {
        hidden = 'msHidden';
        visibilityChange = 'msvisibilitychange';
    } else if (typeof document.webkitHidden !== 'undefined') {
        hidden = 'webkitHidden';
        visibilityChange = 'webkitvisibilitychange';
    }

    document.addEventListener(visibilityChange, function() {
        if (document[hidden]) {
            // Page is hidden - user might be leaving
            gotham_send_analytics('page_hidden', 'tracking');
        } else {
            // Page is visible again
            gotham_send_analytics('page_visible', 'tracking');
        }
    });
}

console.log('Gotham Analytics: tracker script loaded');

// Initialize tracking when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize session tracking
    gotham_get_session_start();

    // Initialize page visibility tracking
    gotham_track_page_visibility();
});

// Use MutationObserver to detect popup display and send analytics event
(function() {
    let sent = false;
    let checkCount = 0;

    // Check if we've already tracked popup display in this session
    const sessionKey = 'gotham_popup_tracked_' + Date.now().toString().slice(0, -6); // Session based on hour
    if (sessionStorage.getItem(sessionKey)) {
        console.log('Gotham Analytics: Popup already tracked in this session, skipping...');
        return;
    }

    function sendIfPopupVisible() {
        checkCount++;
        const popup = document.getElementById('gothamadblock_msg');

        if (!sent && popup) {
            // Enhanced visibility checks to ensure popup is truly visible to the user
            const style = window.getComputedStyle(popup);
            const rect = popup.getBoundingClientRect();

            const isVisible = style.display !== 'none' &&
                            style.visibility !== 'hidden' &&
                            style.opacity !== '0' &&
                            popup.offsetHeight > 0 &&
                            popup.offsetWidth > 0 &&
                            rect.width > 0 &&
                            rect.height > 0 &&
                            // Check if popup is actually in viewport
                            rect.top < window.innerHeight &&
                            rect.bottom > 0 &&
                            rect.left < window.innerWidth &&
                            rect.right > 0;

            if (isVisible) {
                // Additional check: ensure popup has been visible for at least 100ms
                // to avoid tracking false positives from rapid DOM changes
                setTimeout(function() {
                    const popupStillVisible = document.getElementById('gothamadblock_msg');
                    if (popupStillVisible && !sent) {
                        const finalStyle = window.getComputedStyle(popupStillVisible);
                        const finalRect = popupStillVisible.getBoundingClientRect();

                        if (finalStyle.display !== 'none' &&
                            finalStyle.visibility !== 'hidden' &&
                            finalRect.height > 0) {

                            sent = true;
                            // Mark this session as having tracked popup display
                            sessionStorage.setItem(sessionKey, 'true');
                            console.log('Gotham Analytics: Popup confirmed visible to user, sending analytics...');
                            if (typeof gotham_send_analytics === 'function') {
                                gotham_send_analytics('pop_displayed', 'confirmed');
                            } else {
                                console.warn('Gotham Analytics: gotham_send_analytics function not available');
                            }
                        }
                    }
                }, 100);
            }
        }

        // Debug logging
        if (checkCount <= 5) {
            console.log('Gotham Analytics: Popup check #' + checkCount + ', popup found:', !!popup, 'sent:', sent);
        }
    }

    // Set up MutationObserver
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(sendIfPopupVisible);
        observer.observe(document.body, { childList: true, subtree: true });
        console.log('Gotham Analytics: MutationObserver set up for popup detection');
    } else {
        console.warn('Gotham Analytics: MutationObserver not supported, using fallback');
        // Fallback for older browsers
        setInterval(sendIfPopupVisible, 1000);
    }

    // Also check immediately in case popup is already present
    sendIfPopupVisible();

    // Additional check after a short delay
    setTimeout(sendIfPopupVisible, 500);
    setTimeout(sendIfPopupVisible, 2000);
})();

// Call this when user disables adblock and continues
window.gotham_adblock_conversion = function() {
    console.log('Gotham Analytics: Conversion triggered - user disabled adblock');
    gotham_send_analytics('adblock_disabled', 'completed');
}

// Enhanced button click tracking
document.addEventListener('DOMContentLoaded', function() {
    console.log('Gotham Analytics: Setting up button click tracking...');

    // Function to set up button tracking
    function setupButtonTracking() {
        const button = document.getElementById('gtab_mehn');
        if (button && !button.hasAttribute('data-gotham-tracked')) {
            button.setAttribute('data-gotham-tracked', 'true');
            console.log('Gotham Analytics: Button found, setting up click tracking');

            button.addEventListener('click', function() {
                console.log('Gotham Analytics: Button clicked, tracking popup_closed event');
                gotham_send_analytics('popup_closed', 'user_action');

                // Also track potential conversion after a delay
                setTimeout(function() {
                    console.log('Gotham Analytics: Checking for conversion after button click...');
                    // This will be called by the main plugin if adblock is actually disabled
                }, 1000);
            });
        }
    }

    // Try to set up tracking immediately
    setupButtonTracking();

    // Also try after popup might be created
    setTimeout(setupButtonTracking, 1000);
    setTimeout(setupButtonTracking, 3000);

    // Use MutationObserver to catch dynamically added buttons
    if (typeof MutationObserver !== 'undefined') {
        const buttonObserver = new MutationObserver(setupButtonTracking);
        buttonObserver.observe(document.body, { childList: true, subtree: true });
    }
});
