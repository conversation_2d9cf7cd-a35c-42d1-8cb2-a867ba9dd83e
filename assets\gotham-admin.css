/* Gotham Block Admin - Modern WordPress Admin Styling */

/* Main Container */
.gotham_ad_wrap {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fb;
    margin: 20px 0 0 0;
    padding: 0;
}

/* Header Section */
#logo_admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    text-align: center;
    padding: 40px 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    font-size: 2.2em;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Layout Grid */
.gotham_ad_form {
    width: 75%;
    float: left;
    margin-right: 3%;
}

.gotham_ad_credit {
    width: 22%;
    float: left;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    padding: 25px;
    border: 1px solid #e1e5e9;
}

/* Modern Card Design */
.gotham-config-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    margin-bottom: 25px;
    border: 1px solid #e1e5e9;
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.gotham-config-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.gotham-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #e1e5e9;
}

.gotham-card-header h3 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.gotham-card-header .card-icon {
    font-size: 1.2em;
    color: #667eea;
}

.gotham-card-body {
    padding: 25px;
}

.gotham-card-description {
    color: #6c757d;
    font-size: 0.95em;
    line-height: 1.6;
    margin-bottom: 20px;
}

/* Detection Mode Cards */
.detection-modes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.mode-card {
    background: #ffffff;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.mode-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
}

.mode-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.mode-card .mode-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.mode-card .mode-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 0.9em;
}

.mode-card.ssj1 .mode-icon { background: linear-gradient(135deg, #4CAF50, #45a049); }
.mode-card.ssj2 .mode-icon { background: linear-gradient(135deg, #FF9800, #f57c00); }
.mode-card.ssj3 .mode-icon { background: linear-gradient(135deg, #F44336, #d32f2f); }
.mode-card.paused .mode-icon { background: linear-gradient(135deg, #9E9E9E, #757575); }

.mode-card .mode-title {
    font-size: 1.2em;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.mode-card .mode-description {
    color: #6c757d;
    font-size: 0.9em;
    line-height: 1.5;
    margin-bottom: 15px;
}

.mode-card .mode-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mode-card .mode-features li {
    padding: 5px 0;
    font-size: 0.85em;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mode-card .mode-features li::before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
}

/* Form Controls */
.gotham-form-group {
    margin-bottom: 25px;
}

.gotham-form-label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 0.95em;
}

.gotham-form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #ffffff;
}

.gotham-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.gotham-form-control.large {
    min-height: 120px;
    resize: vertical;
    font-family: inherit;
}

/* Cookie Time Selector */
.cookie-time-selector {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.cookie-time-selector label {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 8px;
    display: block;
}

/* Popup Delay Setting Styles */
.popup-delay-selector {
    margin-top: 20px;
    padding: 15px;
    background: #f0f8ff;
    border-radius: 8px;
    border: 1px solid #b3d9ff;
}

.popup-delay-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9em;
}

.gotham-form-group.inline {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.gotham-form-control.small {
    width: 80px;
    text-align: center;
    font-weight: 600;
}

.input-suffix {
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.setting-description {
    font-size: 0.85em;
    color: #666;
    line-height: 1.4;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    border-left: 3px solid #007cba;
}

/* Submit Button */
.gotham-submit-section {
    background: #ffffff;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e1e5e9;
    text-align: center;
    margin-top: 25px;
}

.gotham-submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.gotham-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Credit Section */
.gotham_ad_credit h3 {
    color: #2c3e50;
    font-size: 1.3em;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e1e5e9;
}

.gotham_ad_credit .inner {
    margin-bottom: 15px;
    line-height: 1.6;
}

.gotham_ad_credit h4 {
    color: #495057;
    font-size: 1.1em;
    margin: 20px 0 10px 0;
}

.gotham_ad_credit ul {
    list-style: none;
    padding: 0;
}

.gotham_ad_credit li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.gotham_ad_credit li:last-child {
    border-bottom: none;
}

.gotham_ad_credit a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.gotham_ad_credit a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .gotham_ad_form {
        width: 70%;
        margin-right: 5%;
    }
    
    .gotham_ad_credit {
        width: 25%;
    }
}

@media (max-width: 768px) {
    .gotham_ad_form,
    .gotham_ad_credit {
        width: 100%;
        float: none;
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .detection-modes {
        grid-template-columns: 1fr;
    }
    
    #logo_admin {
        font-size: 1.8em;
        padding: 30px 15px;
    }
}

/* Animation for mode selection */
@keyframes modeSelect {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.mode-card.selecting {
    animation: modeSelect 0.3s ease;
}

/* Hidden elements */
.hidden {
    display: none !important;
}

/* Success message styling */
.gotham-success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #c3e6cb;
    margin-bottom: 20px;
}

/* WordPress admin integration */
.wrap .gotham_ad_wrap {
    margin: 0;
    background: transparent;
}

/* Override WordPress default button styling for our custom button */
.gotham-submit-btn.button-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    text-shadow: none !important;
    font-weight: 600 !important;
    padding: 15px 40px !important;
    height: auto !important;
    line-height: 1.2 !important;
}

.gotham-submit-btn.button-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

.gotham-submit-btn.button-primary:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3), 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
}

/* Clear floats */
.gotham_ad_wrap::after {
    content: "";
    display: table;
    clear: both;
}

/* Improved spacing */
.gotham_ad_form {
    margin-bottom: 30px;
}

/* Enhanced credit section styling */
.gotham_ad_credit .inside {
    padding: 0;
}

.gotham_ad_credit hr {
    border: none;
    border-top: 1px solid #e1e5e9;
    margin: 20px 0;
}

/* Responsive improvements */
@media (max-width: 480px) {
    #logo_admin {
        font-size: 1.5em;
        padding: 20px 10px;
    }

    .gotham-card-body {
        padding: 20px;
    }

    .mode-card {
        padding: 15px;
    }

    .gotham-submit-btn {
        width: 100%;
        padding: 12px 20px !important;
    }
}
