// Standalone uBlock Origin detection test
// This file can be used to test if the enhanced detection works

function testUBlockOriginDetection() {
    console.log('🔍 Starting comprehensive uBlock Origin detection test...');
    
    let detectionMethods = [];
    
    // Method 1: CSS Class Blocking Test
    function testCSSBlocking() {
        console.log('Testing CSS blocking...');
        const testElement = document.createElement('div');
        testElement.className = 'ad-banner advertisement ads google-ads adsense';
        testElement.style.cssText = 'width: 1px; height: 1px; position: absolute; left: -1000px;';
        document.body.appendChild(testElement);
        
        setTimeout(() => {
            const blocked = testElement.offsetHeight === 0 || 
                          window.getComputedStyle(testElement).display === 'none';
            console.log('CSS blocking test:', blocked ? '✅ BLOCKED' : '❌ NOT BLOCKED');
            document.body.removeChild(testElement);
            detectionMethods.push({method: 'CSS Blocking', detected: blocked});
        }, 100);
    }
    
    // Method 2: Network Request Blocking Test
    function testNetworkBlocking() {
        console.log('Testing network blocking...');
        const controller = new AbortController();
        setTimeout(() => controller.abort(), 2000);
        
        fetch('https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js', {
            method: 'HEAD',
            mode: 'no-cors',
            signal: controller.signal
        })
        .then(() => {
            console.log('Network blocking test: ❌ NOT BLOCKED');
            detectionMethods.push({method: 'Network Blocking', detected: false});
        })
        .catch(() => {
            console.log('Network blocking test: ✅ BLOCKED');
            detectionMethods.push({method: 'Network Blocking', detected: true});
        });
    }
    
    // Method 3: Script Injection Test
    function testScriptBlocking() {
        console.log('Testing script blocking...');
        const script = document.createElement('script');
        script.src = 'data:text/javascript;base64,Y29uc29sZS5sb2coJ0FkIHNjcmlwdCBsb2FkZWQnKTs=';
        let blocked = true;
        
        script.onload = () => {
            blocked = false;
            console.log('Script blocking test: ❌ NOT BLOCKED');
            detectionMethods.push({method: 'Script Blocking', detected: false});
        };
        
        script.onerror = () => {
            console.log('Script blocking test: ✅ BLOCKED');
            detectionMethods.push({method: 'Script Blocking', detected: true});
        };
        
        document.head.appendChild(script);
        
        setTimeout(() => {
            if (blocked) {
                console.log('Script blocking test: ✅ BLOCKED (timeout)');
                detectionMethods.push({method: 'Script Blocking', detected: true});
            }
            try { document.head.removeChild(script); } catch(e) {}
        }, 1000);
    }
    
    // Method 4: DOM Mutation Test
    function testDOMMutation() {
        console.log('Testing DOM mutation...');
        const container = document.createElement('div');
        container.innerHTML = '<div class="adsense-ad">Test Ad</div>';
        container.style.cssText = 'position: absolute; left: -1000px;';
        document.body.appendChild(container);
        
        const originalHTML = container.innerHTML;
        
        setTimeout(() => {
            const mutated = container.innerHTML !== originalHTML || 
                          container.children.length === 0;
            console.log('DOM mutation test:', mutated ? '✅ DETECTED' : '❌ NOT DETECTED');
            document.body.removeChild(container);
            detectionMethods.push({method: 'DOM Mutation', detected: mutated});
        }, 200);
    }
    
    // Method 5: uBlock Origin Signature Test
    function testUBlockSignatures() {
        console.log('Testing uBlock Origin signatures...');
        const signatures = [
            () => document.querySelector('style[data-ublock]'),
            () => document.querySelector('[data-ublock-id]'),
            () => window.uBlockOrigin !== undefined,
            () => document.documentElement.hasAttribute('data-ublock'),
            () => document.querySelector('link[href*="ublock"]'),
            () => document.querySelector('script[src*="ublock"]')
        ];
        
        let signatureFound = false;
        signatures.forEach((check, index) => {
            try {
                if (check()) {
                    console.log(`uBlock signature ${index + 1}: ✅ FOUND`);
                    signatureFound = true;
                }
            } catch(e) {}
        });
        
        console.log('uBlock signatures test:', signatureFound ? '✅ DETECTED' : '❌ NOT DETECTED');
        detectionMethods.push({method: 'uBlock Signatures', detected: signatureFound});
    }
    
    // Run all tests
    testCSSBlocking();
    testNetworkBlocking();
    testScriptBlocking();
    testDOMMutation();
    testUBlockSignatures();
    
    // Summary after all tests
    setTimeout(() => {
        console.log('\n📊 DETECTION SUMMARY:');
        console.log('===================');
        
        let detectedCount = 0;
        detectionMethods.forEach(method => {
            console.log(`${method.method}: ${method.detected ? '✅ DETECTED' : '❌ NOT DETECTED'}`);
            if (method.detected) detectedCount++;
        });
        
        const confidence = (detectedCount / detectionMethods.length) * 100;
        console.log(`\n🎯 OVERALL RESULT: ${detectedCount}/${detectionMethods.length} methods detected blocking`);
        console.log(`📈 CONFIDENCE LEVEL: ${confidence.toFixed(1)}%`);
        
        if (confidence >= 60) {
            console.log('🚫 VERDICT: uBlock Origin or similar advanced ad blocker DETECTED');
        } else if (confidence >= 30) {
            console.log('⚠️ VERDICT: Some ad blocking detected, but may not be uBlock Origin');
        } else {
            console.log('✅ VERDICT: No significant ad blocking detected');
        }
    }, 3000);
}

// Auto-run test when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testUBlockOriginDetection);
} else {
    testUBlockOriginDetection();
}

// Make function available globally for manual testing
window.testUBlockOriginDetection = testUBlockOriginDetection;
