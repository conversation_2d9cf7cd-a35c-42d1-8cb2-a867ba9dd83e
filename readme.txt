=== Gotham Block Extra Light ===

Contributors: kapsulecorp
Donate link: https://www.kapsulecorp.com/
Tags: Adblock, ads, block Adblock, detect Adblock

Stable tag: 1.5.0
Tested up to: 6.8
Requires at least: 6.0
Requires PHP: 7.4.0

License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html


== Upgrade Notice ==

ULTRA Light plugin to BLOCK visitors with adblocker - or just to INFORM your visitors that adblockers are killing the viability of your site and invite them to deactivate them.

== Description ==

This plugin detects if the user's browser has AdBlock software activated and displays a popup if this is the case (screenshot 1). The popup is 100% customizable.

In addition, the aggressiveness of this popup is adjustable:

- LEVEL 1: The popup will inform the user that blocking ads kills your buisness model and invites them to deactivate their adblocker. The message will only be displayed once every x minutes/hours/days, as long as the user has Adblock software activated, but the user can close the popup and continue browsing your site normally (even with Adblock activated)

- LEVEL 2: The popup will inform the user that blocking ads kills your buisness model and invites him to deactivate his adblocker. The message will appear on each page load, as long as the user has Adblock software activated, however the user can each time close the popup and continue browsing your site normally (even with Adblock activated)

- LEVEL 3: The popup will inform the user that blocking ads kills your buisness model and invites them to deactivate their adblocker. The message will appear on each page load, and the user will not be able to navigate on your site as long as he has Adblock software activated.

This plugin is 100% Responsive and compatible with PC - Mac - Tablets and Smartphones on iOS - Android - Windows. It is compatible with Edge, Firefox, Google Chrome, Safari browsers.

You can also easily customize the popup message and the close button in the setting page of this plugin (screenshot 2). It's up to you to find the right words to make the Internet user feel guilty by adapting the message to your audience.

This plugin is by default in English and French. But as you can customize the text in the admin, you can adapt the popup in the language of your choice.

⭐ PREMIUM VERSION ⭐

Add Unblock Ads Banners Widget for Premium. Are you tired of seeing your banners hidden by Adblock? With PREMIUM mode, configure in a shortcode or in a widget in 30 seconds: the url of the advertisement, the url of the image (and optionally its dimensions, its alt tag, its way of opening, if the url will be cloaked or not) to pass through Adblock. The advertising banner will be stored on your server and will be updated every 24 hours to always be in line with the advertiser's offers, and never to become an old has-been banner!

/////// IN FRENCH ////

Ce plugin détecte si le navigateur de l'internaute a un logiciel AdBlock d'activé et affiche une popup si c'est le cas (screenshot 1). La popup est 100% customisable. 

De plus, l'agressivité de cette popup est réglable :

- NIVEAU 1: La popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invite à désactiver son adblocker. Le message ne s'affichera qu'une fois tous les x minutes/heures/jours, tant que l'internaute aura un logiciel Adblock activé, mais l'internaute pourra fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)

- NIVEAU 2 : La popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invite à désactiver son adblocker. Le message s'affichera à chaque chargement de page, tant que l'internaute aura un logiciel Adblock activé, cependant l'internaute pourra à chaque fois fermer la popup et continuer à naviguer sur votre site normalement (même avec Adblock activé)

- NIVEAU 3 : La popup informera l'internaute que bloquer les publicités tue votre buisness model et l'invite à désactiver son adblocker. Le message s'affichera à chaque chargement de page, et l'internaute ne pourra pas naviguer sur votre site tant qu'il aura un logiciel Adblock activé.

Ce plugin est 100% Responsive et compatible PC - Mac - Tablettes et Smartphones sous iOS - Android - Windows. Il est compatible avec les navigateurs Edge, Firefox, Google Chrome, Safari.

Vous pourrez par ailleurs personnaliser très facilement le message de la popup ainsi que le bouton de fermeture dans la page de réglage de ce plugin (screenshot 2) . A vous de trouver les bons mots pour faire culpabiliser l'internaute en adaptant le message à votre public.

Ce plugin est par défaut en Anglais et en Français. Mais comme vous pouvez personnaliser le texte dans l'admin, vous pourrez donc adapter la popup dans la langue de votre choix.

⭐ PREMIUM VERSION ⭐

Vous en avez marre de voir vos bannières masquées par Adblock ? Avec le mode PREMIUM, paramétrez dans un shortcode ou dans un widget en 30 secondes : l'url de la publicité, l'url de l'image ( et en option ses dimensions, sa balise alt, sa façon de s'ouvrir, si l'url sera cloaké ou non) pour passer à travers Adblock. La bannière publicitaire sera stocké sur votre serveur et sera mise à jour toutes les 24H pour être toujours en adéquation avec les offres de l'annonceur, et ne jamais devenir une vieille bannière has-been !


== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/plugin-name` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Click to G BlockAdblock (Settings) on the left menu to configure the plugin.


== Frequently Asked Questions ==

= This plugin is really light? =

YES lighter than that, it does not exist! 

= This plugin is really light? =

Yes absolutely!

== Screenshots ==

1. This screen shot description corresponds to screenshot-1.png. Note that the screenshot is taken from the directory that contains the stable readme.txt.

> This screenshot show the default message popup (fully customisable)

2. This screen shot description corresponds to screenshot-2.png. Note that the screenshot is taken from the directory that contains the stable readme.txt.

> This screenshot show the admin panel (Available in French & English)

3. This screen shot description corresponds to screenshot-3.png. Note that the screenshot is taken from the directory that contains the stable readme.txt.

> This screenshot show the PREMIUM Widget (Available in French & English). Configure, the url of the advertisement, the url of the image (and optionally its dimensions, its alt tag, its way of opening, if the url will be cloaked or not) to pass through Adblock. The advertising banner will be stored on your server and will be updated every 24 hours to always be in line with the advertiser's offers, and never to become an old has-been banner!


== Changelog ==

= 1.5.0 =

- Fix Minor Bug

= 1.4.9 =

- Fix Minor Bug

= 1.4.8 =

- Hide Error when distant server block copy function on PREMIUM

= 1.4.7 =

- Ready for Wordpress 6

= 1.4.6 =

- Fix bug with Smartlink JS

= 1.4.5 =

- Optimise Smartlink JS

= 1.4.1 =

- Fix bug in back office
- Wordpress 5.8 OK

= 1.4.0 =
- Add Unblock Ads Banners Widget for Premium. Are you tired of seeing your banners hidden by Adblock? With PREMIUM mode, configure in a shortcode or in a widget in 30 seconds: the url of the advertisement, the url of the image (and optionally its dimensions, its alt tag, its way of opening, if the url will be cloaked or not) to pass through Adblock. The advertising banner will be stored on your server and will be updated every 24 hours to always be in line with the advertiser's offers, and never to become an old has-been banner!

= 1.3.6 =
- Better Sanitization

= 1.3.5 =
- Change CSS Name for prevent potnetial conflict name
- Add Image Size

= 1.3.4 =
- Add capability to set a period to hide AdBlock Popup Detection in SSJ1
- Fix bug with cookie

= 1.3.3 =
- Fix bug to restore scrolling after closing the popup (with className)
- Fix bug related to break line with custom message 

= 1.3.2 =
- Fix bug to restore scrolling in SSJ2 : Thanks to @rajaanbazhagan

= 1.3.1 =
- Block scroll when popup is displayed

= 1.2 =
- Optimize Technology to detect Adblock.
- Fix False Positive Detection

= 1.1.1 =
- Minor Update

= 1.1 =
- Minor Update

= 1.0 =
- Plugin launch

== Bonus ==

Here's a link to [Fournisseur Acces Internet](https://www.fournisseur-acces-internet.com/ "Meilleur FAI") and one to [Balise META](https://www.balisemeta.com/ "Balise HTML") ! It's my gift for you !