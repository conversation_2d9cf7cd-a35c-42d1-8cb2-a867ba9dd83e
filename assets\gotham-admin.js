// Gotham Block Admin - Modern Interface JavaScript

class GothamAdminInterface {
    constructor() {
        this.currentMode = null;
        this.init();
    }

    init() {
        this.setupModeCards();
        this.setupFormValidation();
        this.setupTooltips();
        this.initializeCurrentMode();
    }

    setupModeCards() {
        const modeCards = document.querySelectorAll('.mode-card');
        const modeSelect = document.getElementById('gothamadblock_option_fury');
        const cookieTimeContainer = document.querySelector('.cookie-time-selector');

        modeCards.forEach(card => {
            card.addEventListener('click', () => {
                const mode = card.dataset.mode;
                this.selectMode(mode, card, modeSelect, cookieTimeContainer);
            });
        });

        // Also listen to the hidden select element changes
        if (modeSelect) {
            modeSelect.addEventListener('change', (e) => {
                const selectedCard = document.querySelector(`[data-mode="${e.target.value}"]`);
                if (selectedCard) {
                    this.selectMode(e.target.value, selectedCard, modeSelect, cookieTimeContainer);
                }
            });
        }
    }

    selectMode(mode, card, modeSelect, cookieTimeContainer) {
        // Remove selection from all cards
        document.querySelectorAll('.mode-card').forEach(c => {
            c.classList.remove('selected');
        });

        // Add selection to clicked card
        card.classList.add('selected', 'selecting');
        setTimeout(() => card.classList.remove('selecting'), 300);

        // Update hidden select
        if (modeSelect) {
            modeSelect.value = mode;
        }

        // Show/hide cookie time selector
        if (cookieTimeContainer) {
            if (mode === 'ssj1') {
                cookieTimeContainer.style.display = 'block';
                this.animateIn(cookieTimeContainer);
            } else {
                this.animateOut(cookieTimeContainer);
            }
        }

        this.currentMode = mode;
        this.showModeInfo(mode);
    }

    initializeCurrentMode() {
        const modeSelect = document.getElementById('gothamadblock_option_fury');
        if (modeSelect && modeSelect.value) {
            const selectedCard = document.querySelector(`[data-mode="${modeSelect.value}"]`);
            const cookieTimeContainer = document.querySelector('.cookie-time-selector');
            if (selectedCard) {
                this.selectMode(modeSelect.value, selectedCard, modeSelect, cookieTimeContainer);
            }
        }
    }

    showModeInfo(mode) {
        const infoMessages = {
            ssj1: {
                title: "Light Mode Selected",
                message: "Users will see the popup once per configured time period. They can close it and continue browsing.",
                type: "info"
            },
            ssj2: {
                title: "Aggressive Mode Selected", 
                message: "Users will see the popup on every page load but can still close it and continue browsing.",
                type: "warning"
            },
            ssj3: {
                title: "Fury Mode Selected",
                message: "Users cannot browse your site until they disable their adblocker. Use with caution!",
                type: "error"
            },
            paused: {
                title: "Plugin Paused",
                message: "Adblock detection is disabled. No popups will be shown to users.",
                type: "neutral"
            }
        };

        const info = infoMessages[mode];
        if (info) {
            this.showNotification(info.title, info.message, info.type);
        }
    }

    showNotification(title, message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelector('.gotham-notification');
        if (existing) {
            existing.remove();
        }

        const notification = document.createElement('div');
        notification.className = `gotham-notification gotham-notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <strong>${title}</strong>
                <p>${message}</p>
            </div>
            <button class="notification-close">&times;</button>
        `;

        // Insert after the mode selection
        const modeSection = document.querySelector('.detection-modes');
        if (modeSection) {
            modeSection.parentNode.insertBefore(notification, modeSection.nextSibling);
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                this.animateOut(notification, () => notification.remove());
            }
        }, 5000);

        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.animateOut(notification, () => notification.remove());
        });

        this.animateIn(notification);
    }

    setupFormValidation() {
        const form = document.querySelector('.gotham_ad_form form');
        if (!form) return;

        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });

        form.addEventListener('submit', (e) => {
            if (!this.validateForm(form)) {
                e.preventDefault();
                this.showNotification(
                    'Validation Error',
                    'Please check the form for errors and try again.',
                    'error'
                );
            }
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // Custom validation rules
        if (field.type === 'text' && field.name.includes('title') && value.length > 100) {
            isValid = false;
            message = 'Title should be less than 100 characters';
        }

        if (field.tagName === 'TEXTAREA' && value.length > 1000) {
            isValid = false;
            message = 'Message should be less than 1000 characters';
        }

        if (isValid) {
            this.clearFieldError(field);
        } else {
            this.showFieldError(field, message);
        }

        return isValid;
    }

    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    setupTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'gotham-tooltip';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('visible'), 10);
    }

    hideTooltip() {
        const tooltip = document.querySelector('.gotham-tooltip');
        if (tooltip) {
            tooltip.classList.remove('visible');
            setTimeout(() => tooltip.remove(), 200);
        }
    }

    animateIn(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';
        element.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 10);
    }

    animateOut(element, callback) {
        element.style.transition = 'all 0.3s ease';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
            if (callback) callback();
        }, 300);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new GothamAdminInterface();
});

// Legacy function for backward compatibility
function besoindelacuisiniere(sel) {
    const mode = sel.value;
    const cookieTimeContainer = document.querySelector('.cookie-time-selector');
    const cookieTimeSelect = document.getElementById('gothamadblock_option_cookietime');
    
    if (cookieTimeContainer && cookieTimeSelect) {
        if (mode === 'ssj1') {
            cookieTimeContainer.style.display = 'block';
            cookieTimeSelect.style.display = 'block';
        } else {
            cookieTimeContainer.style.display = 'none';
            cookieTimeSelect.style.display = 'none';
        }
    }
}
