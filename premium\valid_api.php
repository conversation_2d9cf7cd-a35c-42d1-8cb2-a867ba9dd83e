<?php 
// exit if accessed directly
if ( ! defined( 'ABSPATH' ) )
	exit;

$ghostpremium_doubleface = false;
$ghostpremium_home_url = get_home_url();
$ghostpremium_home_url = str_replace("https://", "", $ghostpremium_home_url);
$ghostpremium_home_url = str_replace("http://", "", $ghostpremium_home_url);
$ghostpremium_home_url = str_replace("www.", "", $ghostpremium_home_url);

$ghostpremium_unik = base64_encode($ghostpremium_home_url);
$ghostpremium_unik = preg_replace("/[^a-zA-Z0-9]/", "", $ghostpremium_unik);

if ( is_multisite() ) {
	
	$ghostpremium_iddusite = get_current_blog_id();
	$ghostpremium_lkey = "$ghostpremium_unik-NJD54-$ghostpremium_iddusite";
	
} else {
	
	$ghostpremium_lkey = "SJFEDD54-$ghostpremium_unik";
	
}

$ghostpremium_cookie_token = GOTHAMBKOCKADBLOCK_ROOTPATH . "temp/token-$ghostpremium_lkey.json";
$ghostpremium_newjeton = false;

// L'URL est-elle bien déclarée
$ghostpremium_url = get_site_url(); 

function ghostpremium_check_site_current_url ($url) {
	$url = str_replace("https://", "", $url);
	$url = str_replace("http://", "", $url);
	$url = str_replace("www.", "", $url);
	
	if (substr($url, -1) == '/') {
		$url = substr($url,0,strlen($url)-1);
	}
	
	return $url;
}

$ghostpremium_choppezla = ghostpremium_check_site_current_url($ghostpremium_url);	

	
// On vire le cache token quand on update la clé API
function check_licence_update_ghostpremium(){

		global $ghostpremium_cookie_token;
		
		if (file_exists($ghostpremium_cookie_token)) { // S'il existe
			unlink ($ghostpremium_cookie_token);
		}
		
		check_kapsuleapi_ghostpremium_licence();
		
}

// On check si API valide
function check_kapsuleapi_ghostpremium_licence() {
	
	return "FREETRIAL";
	
}