/* analytics-admin.css - Enhanced dashboard styles */
.gotham-analytics-admin {
    font-family: 'Segoe UI', Arial, sans-serif;
    background: #f8f9fb;
    padding: 20px;
}

/* Dashboard Controls */
.gotham-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: 600;
    font-size: 0.9em;
    color: #333;
}

.control-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Summary Cards */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #3f51b5;
}

.summary-card.conversion { border-left-color: #4caf50; }
.summary-card.declined { border-left-color: #f44336; }
.summary-card.rate { border-left-color: #ff9800; }

.summary-card .metric-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

.summary-card .metric-label {
    font-size: 0.9em;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-card .metric-description {
    font-size: 0.75em;
    color: #999;
    margin-top: 3px;
    line-height: 1.3;
    font-style: italic;
    text-transform: none;
    letter-spacing: normal;
}

/* Data Quality Section */
.data-quality-section {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
}

.data-quality-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3em;
    font-weight: 600;
}

.quality-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.quality-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    text-align: center;
    transition: all 0.3s ease;
}

.quality-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quality-card.human-verified {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
}

.quality-card .metric-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
}

.quality-card.human-verified .metric-value {
    color: #28a745;
    font-size: 2.2em;
}

.quality-card .metric-label {
    font-size: 0.9em;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.quality-card .metric-description {
    font-size: 0.75em;
    color: #999;
    line-height: 1.3;
    font-style: italic;
}

.summary-card .metric-change {
    font-size: 0.8em;
    margin-top: 8px;
    padding: 4px 8px;
    border-radius: 12px;
}

.metric-change.positive { background: #e8f5e8; color: #2e7d32; }
.metric-change.negative { background: #ffebee; color: #c62828; }

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.gotham-analytics-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 24px;
}

.gotham-analytics-title {
    font-size: 1.3em;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.gotham-analytics-chart {
    width: 100% !important;
    height: 400px !important;
}

.chart-small {
    height: 300px !important;
}

/* Funnel Chart */
.funnel-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.funnel-step {
    display: flex;
    align-items: center;
    padding: 15px;
    background: linear-gradient(90deg, #3f51b5, #5c6bc0);
    color: white;
    border-radius: 8px;
    position: relative;
}

.funnel-step:nth-child(2) { background: linear-gradient(90deg, #4caf50, #66bb6a); }
.funnel-step:nth-child(3) { background: linear-gradient(90deg, #ff9800, #ffb74d); }

.funnel-step .step-number {
    font-size: 1.5em;
    font-weight: bold;
    margin-right: 15px;
    width: 40px;
    text-align: center;
}

.funnel-step .step-content {
    flex: 1;
}

.funnel-step .step-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.funnel-step .step-value {
    font-size: 1.2em;
    font-weight: bold;
}

.funnel-step .step-percentage {
    font-size: 0.9em;
    opacity: 0.9;
}

/* Loading and Error States */
#loading-indicator {
    text-align: center;
    padding: 40px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

#loading-indicator p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

#loading-indicator::before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3f51b5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Data State */
.no-data-message {
    text-align: center;
    padding: 50px;
    color: #666;
    background: #f9f9f9;
    border-radius: 8px;
    border: 2px dashed #ddd;
}

.no-data-message h3 {
    color: #333;
    margin-bottom: 15px;
}

.no-data-message p {
    margin: 10px 0;
    line-height: 1.6;
}

/* Error Message Styling */
#error-message {
    margin-bottom: 20px;
}

#error-message p {
    margin: 0;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .gotham-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .gotham-analytics-chart {
        height: 300px !important;
    }
}
